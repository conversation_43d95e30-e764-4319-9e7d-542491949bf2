import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { GraduationCap } from './icons/index';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import { Scholarship } from './ScholarshipGrid';
import scholarshipService from '../services/scholarshipService';

interface EnhancedStudyLevelSectionProps {
  // You can add props here if needed
}

const EnhancedStudyLevelSection: React.FC<EnhancedStudyLevelSectionProps> = () => {
  const [activeTab, setActiveTab] = useState('licence');
  const tabsRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [scholarships, setScholarships] = useState<{
    licence: Scholarship[];
    master: Scholarship[];
    doctorat: Scholarship[];
  }>({
    licence: [],
    master: [],
    doctorat: []
  });

  // Fetch scholarships by level
  useEffect(() => {
    const fetchScholarshipsByLevel = async () => {
      try {
        setLoading(true);

        // Fetch all scholarships
        const allScholarships = await scholarshipService.getAllScholarships();

        // Filter scholarships by level more precisely
        const licenceScholarships = allScholarships.filter(scholarship => {
          const level = scholarship.level?.toLowerCase() || '';
          return level === 'licence' ||
                 level === 'undergraduate' ||
                 level === 'bachelor' ||
                 (level.includes('licence') && !level.includes('master') && !level.includes('doctorat')) ||
                 (level.includes('undergraduate') && !level.includes('graduate') && !level.includes('phd'));
        });

        const masterScholarships = allScholarships.filter(scholarship => {
          const level = scholarship.level?.toLowerCase() || '';
          return level === 'master' ||
                 level === 'graduate' ||
                 (level.includes('master') && !level.includes('licence') && !level.includes('doctorat')) ||
                 (level.includes('graduate') && !level.includes('undergraduate') && !level.includes('phd'));
        });

        const doctoratScholarships = allScholarships.filter(scholarship => {
          const level = scholarship.level?.toLowerCase() || '';
          return level === 'doctorat' ||
                 level === 'phd' ||
                 level === 'doctorate' ||
                 (level.includes('doctorat') && !level.includes('licence') && !level.includes('master')) ||
                 (level.includes('phd') && !level.includes('undergraduate') && !level.includes('graduate'));
        });

        setScholarships({
          licence: licenceScholarships,
          master: masterScholarships,
          doctorat: doctoratScholarships
        });
      } catch (error) {
        console.error('Error fetching scholarships by level:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchScholarshipsByLevel();
  }, []);

  // Get active scholarships based on tab
  const getActiveScholarships = () => {
    switch (activeTab) {
      case 'licence':
        return scholarships.licence;
      case 'master':
        return scholarships.master;
      case 'doctorat':
        return scholarships.doctorat;
      default:
        return scholarships.licence;
    }
  };

  const activeScholarships = getActiveScholarships();

  // Tab configuration
  const tabs = [
    {
      id: 'licence',
      title: 'Licence',
      color: 'blue',
      icon: <GraduationCap className="w-5 h-5" />,
      benefits: ['Frais de scolarité', 'Allocation mensuelle', 'Frais de voyage'],
      bgClass: 'from-blue-500 to-blue-700'
    },
    {
      id: 'master',
      title: 'Master',
      color: 'purple',
      icon: <GraduationCap className="w-5 h-5" />,
      benefits: ['Frais de scolarité', 'Allocation mensuelle', 'Assurance santé'],
      bgClass: 'from-purple-500 to-purple-700'
    },
    {
      id: 'doctorat',
      title: 'Doctorat',
      color: 'indigo',
      icon: <GraduationCap className="w-5 h-5" />,
      benefits: ['Frais de scolarité', 'Allocation de recherche', 'Conférences internationales'],
      bgClass: 'from-indigo-500 to-indigo-700'
    }
  ];

  // Get active tab data
  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0];

  // Handle scholarship click
  const handleScholarshipClick = (id: number, slug?: string) => {
    // Navigate to scholarship details page using slug if available
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  return (
    <section className="py-3 pt-2 bg-gradient-to-br from-white via-primary-50/10 to-primary-100/30 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


        {/* Interactive tabs */}
        <div ref={tabsRef} className="flex flex-wrap justify-center gap-4 mb-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => {
                setActiveTab(tab.id);
                // Just filter on the same page, don't navigate
              }}
              className={`flex items-center px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 border ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-primary to-primary-light text-white border-primary shadow-lg transform scale-105'
                  : 'bg-white border-primary hover:border-primary hover:text-white hover:bg-gradient-to-r hover:from-primary hover:to-primary-light hover:shadow-md'
                } text-primary`}
              style={{
                color: activeTab === tab.id ? 'white' : '#3a206c',
                borderColor: '#3a206c'
              }}
              }`}
            >
              <span className="mr-2">
                {tab.icon}
              </span>
              {tab.title}
            </button>
          ))}
        </div>



        {/* Scholarships grid - 3x2 grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-md overflow-hidden animate-pulse">
                <div className="aspect-[16/9] bg-gray-200"></div>
                <div className="p-5">
                  <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                  <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : activeScholarships.length > 0 ? (
          <div className="gy-pcard-wrap">
            {activeScholarships.slice(0, 6).map((scholarship, index) => (
              <EnhancedScholarshipCard
                key={scholarship.id}
                id={scholarship.id}
                title={scholarship.title}
                thumbnail={scholarship.thumbnail}
                deadline={scholarship.deadline}
                isOpen={scholarship.isOpen}
                level={scholarship.level}
                fundingSource={scholarship.fundingSource}
                country={scholarship.country}
                onClick={handleScholarshipClick}
                index={index}
                variant="greatyop"
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white rounded-2xl shadow-sm">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">Aucune bourse trouvée</h3>
            <p className="mt-1 text-gray-500">Aucune bourse n'est disponible pour ce niveau d'études.</p>
          </div>
        )}

        {/* Call to action */}
        <div className="mt-6 flex justify-center">
          <Link
            to={`/scholarships?level=${activeTabData.title}`}
            className="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-xl text-white shadow-md transition-colors duration-300"
            style={{ backgroundColor: '#3a206c' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#2a105c'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3a206c'}
          >
            Voir toutes les bourses de {activeTabData.title}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default EnhancedStudyLevelSection;
